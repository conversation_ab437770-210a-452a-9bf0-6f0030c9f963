package io.github.clive.luxomssystem.application.finace

import io.github.clive.luxomssystem.application.finace.ComboSpuBundleMatcher.MatchedComboResult
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.customerSku.model.ComboCustomerSku
import io.github.clive.luxomssystem.domain.customerSku.model.CustomerSku
import io.github.clive.luxomssystem.domain.customerSku.model.PriceDetail
import io.github.clive.luxomssystem.domain.customerSpu.model.ComboCustomerSpu
import io.github.clive.luxomssystem.domain.customerSpu.model.CustomerSpu
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.model.CalculateCustomerNeedPayCostData
import io.github.clive.luxomssystem.domain.waybill.model.CalculateCustomerNeedPayCostData.CalculateCustomerNeedPayCostPreOrderData
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.infrastructure.config.properties.WebHookType
import io.github.clive.luxomssystem.infrastructure.remote.LarkRemoteNotifyService
import io.github.clive.luxomssystem.infrastructure.repository.jpa.CountryRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.CustomerCountryTaxConfigRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.ComboCustomerSkuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.ComboCustomerSpuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.CustomerSkuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.CustomerSpuRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionalEventListener
import java.math.BigDecimal
import java.util.Locale
import kotlin.math.min

private interface CustomerSkuArgumentProvider {
    val order: SubOrder

    fun qty(): Int

    fun display(): String

    fun getPrice(
        countryId: Long?,
        remaining: Int,
    ): PriceDetail

    val minTriggerDiscountQuantity: Int
    val discount: BigDecimal

    class ComboCustomerSkuProvider(
        override val order: SubOrder,
        val comboCustomerSku: ComboCustomerSku,
        val comboCustomerSpu: ComboCustomerSpu,
    ) : CustomerSkuArgumentProvider {
        override val minTriggerDiscountQuantity
            get() =
                minOf(
                    comboCustomerSku.triggerDiscountQuantity,
                    comboCustomerSpu.triggerDiscountQuantity,
                )
        override val discount
            get() =
                minOf(
                    comboCustomerSku.discount,
                    comboCustomerSpu.discount,
                )

        override fun qty(): Int = order.product.qty

        override fun display(): String = "套装中[${comboCustomerSku.id} - ${comboCustomerSku.systemSkuCode}]"

        override fun getPrice(
            countryId: Long?,
            remaining: Int,
        ): PriceDetail = comboCustomerSku.getPrice(countryId, remaining)
    }

    class CustomerSkuProvider(
        override val order: SubOrder,
        private val customerSku: CustomerSku,
        private val customerSpu: CustomerSpu,
    ) : CustomerSkuArgumentProvider {
        override val minTriggerDiscountQuantity get() = minOf(customerSku.triggerDiscountQuantity, customerSpu.triggerDiscountQuantity)

        override val discount
            get() =
                minOf(
                    customerSku.discount,
                    customerSpu.discount,
                )

        override fun qty(): Int = order.product.qty

        override fun display(): String = customerSku.systemSkuCode

        override fun getPrice(
            countryId: Long?,
            remaining: Int,
        ) = customerSku.getPrice(countryId, remaining)
    }

    companion object {
        private fun minOf(
            value1: Int?,
            value2: Int,
        ): Int {
            if (value1 == null) return value2
            return min(value1, value2)
        }

        private fun minOf(
            value1: BigDecimal?,
            value2: BigDecimal,
        ): BigDecimal {
            if (value1 == null) return value2
            return if (value1 > value2) {
                value2
            } else {
                value1
            }
        }
    }
}

@Service
class FinanceApplicationService(
    private val countryRepository: CountryRepository,
    private val waybillRepository: WaybillRepository,
    private val subOrderRepository: SubOrderRepository,
    private val customerSpuRepository: CustomerSpuRepository,
    private val customerSkuRepository: CustomerSkuRepository,
    private val comboCustomerSpuRepository: ComboCustomerSpuRepository,
    private val comboCustomerSkuRepository: ComboCustomerSkuRepository,
    private val larkRemoteNotifyService: LarkRemoteNotifyService,
    private val customerCountryTaxConfigRepository: CustomerCountryTaxConfigRepository,
) {
    //    @PostConstruct
    //    fun init() {
    //        onWaybillCompleted(
    //            WaybillPendingEvent(165873144532848736L, 1L)
    //        )
    //    }

    private sealed interface BillingUnit {
        fun getCustomerSkuArgumentProvider(): List<CustomerSkuArgumentProvider>
    }

    private inner class MatchedComboBillingUnitWrapper(
        val waybill: Waybill,
        val matchedComboResult: MatchedComboResult,
    ) : BillingUnit {
        override fun getCustomerSkuArgumentProvider(): List<CustomerSkuArgumentProvider> {
            val comboCustomerSpuId = matchedComboResult.matchedComboSpu.id
            return matchedComboResult.matchedOrders.map {
                val sku =
                    comboCustomerSkuRepository.findByComboCustomerSpuIdAndSystemSkuIdAndCustomerId(
                        comboCustomerSpuId,
                        it.product.skuId!!,
                        it.customerId,
                    )
                if (sku == null) {
                    log.error {
                        "未找到 套装内SKU 信息：客户套装ID= $comboCustomerSpuId SKU ID = ${it.product.skuId}, 运单 ID = ${waybill.id}, 客户 ID = ${waybill.customerId}"
                    }
                    throw IllegalArgumentException(
                        "未找到 套装内SKU 信息：客户套装ID= $comboCustomerSpuId SKU ID = ${it.product.skuId}, 运单 ID = ${waybill.id}, 客户 ID = ${waybill.customerId}",
                    )
                }
                CustomerSkuArgumentProvider.ComboCustomerSkuProvider(it, sku, matchedComboResult.matchedComboSpu)
            }
        }
    }

    private inner class OrderBillingUnitWrapper(
        val waybill: Waybill,
        val order: SubOrder,
    ) : BillingUnit {
        private val customerSpu: CustomerSpu

        init {
            val spu = customerSpuRepository.findBySystemSpuIdAndCustomerId(order.product.spuId!!, waybill.customerId)
            if (spu == null) {
                log.error {
                    "未找到 SPU 信息：SPU ID = ${order.product.spuId}, 运单 ID = ${waybill.id}, 客户 ID = ${waybill.customerId}"
                }
                throw IllegalArgumentException(
                    "未找到 SPU 信息：SPU ID = ${order.product.spuId}, 运单 ID = ${waybill.id}, 客户 ID = ${waybill.customerId}",
                )
            }
            customerSpu = spu
        }

        override fun getCustomerSkuArgumentProvider(): List<CustomerSkuArgumentProvider> {
            val sku =
                customerSkuRepository.findBySystemSkuIdAndCustomerId(
                    order.product.skuId!!,
                    order.customerId,
                )
            if (sku == null) {
                log.error {
                    "未找到 SKU 信息：SKU ID = ${order.product.skuId}, 运单 ID = ${waybill.id}, 客户 ID = ${waybill.customerId}"
                }
                throw IllegalArgumentException(
                    "未找到 SKU 信息：SKU ID = ${order.product.skuId}, 运单 ID = ${waybill.id}, 客户 ID = ${waybill.customerId}",
                )
            }
            return listOf(CustomerSkuArgumentProvider.CustomerSkuProvider(order, sku, customerSpu))
        }
    }

    private fun <T> MutableList<T>.replaceLast(block: T.() -> T) {
        if (isNotEmpty()) {
            this[size - 1] = block(this[this.size - 1])
        }
    }

    @Async
    @Transactional(rollbackFor = [Exception::class], propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(WaybillCompletedEvent::class)
    fun onWaybillCompleted(event: WaybillCompletedEvent) {
        log.info {
            """运单费用计算开始 | WaybillId: ${event.waybillId} 
    |事件类型: 运单完成 
            """.trimMargin()
        }

        waybillRepository.findById(event.waybillId).ifPresent { waybill ->
            val calculateFlow = StringBuilder()

            log.info { "查找运单 ${event.waybillId} 关联的子订单 | OrderNo: ${waybill.orderNos} " }
            val subOrders = subOrderRepository.findByOrderNoIn(waybill.orderNos.split(","))
            log.info { "找到 ${subOrders.size} 个关联子订单 | TraceId: ${event.waybillId} " }
            calculateFlow.append("订单信息:\n")
            calculateFlow.append("  关联子订单: ${subOrders.size}个\n")

            val spuCodes = subOrders.mapNotNull { it.product.spu }
            val uniqueSpuCodes = spuCodes.toSet()

            val billingUnits =
                if (uniqueSpuCodes.size > 1) {
                    // 只有种类数量至少有2件的时候才有匹配套装的意义
                    val comboCustomerSpu =
                        comboCustomerSpuRepository.findAllIfComboContainsAnySpu(
                            uniqueSpuCodes.toTypedArray(),
                            BaseStatus.ENABLED.name,
                            waybill.customerId,
                        )
                    run {
                        // 如果套装之间存在交集,可能会出现多个套装匹配结果,此时价格是不一定对的
                        val uniqueSpuCodesInCombo = mutableSetOf<String>()
                        comboCustomerSpu.forEach {
                            it.systemSpuCodes.forEach { spuCode ->
                                if (spuCode in uniqueSpuCodesInCombo) {
                                    larkRemoteNotifyService.sendNotificationAsync(
                                        WebHookType.WAY_BILL_FINANCE_CALC_COMBO_CROSS_ERROR,
                                        LarkRemoteNotifyService.LarkMessage(
                                            "运单计价异常",
                                            "运单:${waybill.id} | orderNo: ${waybill.orderNos} , 套装存在交集,无法匹配",
                                        ),
                                    )
                                    throw IllegalArgumentException("运单:${waybill.id} | orderNo: ${waybill.orderNos} , 套装存在交集,无法匹配")
                                }
                                uniqueSpuCodesInCombo.add(spuCode)
                            }
                        }
                    }

                    val comboSpuBundleMatcher = ComboSpuBundleMatcher(comboCustomerSpu)
                    val comboSpuMatchResult = comboSpuBundleMatcher.match(subOrders)
                    val matchMessage =
                        buildString {
                            appendLine("套装数量: ${comboSpuMatchResult.matchedComboResult.size}")
                            comboSpuMatchResult.matchedComboResult.forEach { matchedComboResult ->
                                append("套装${matchedComboResult.matchedComboSpu.id}: [")
                                append(matchedComboResult.matchedComboSpu.systemSpuCodes.joinToString(" , "))
                                appendLine("]")
                            }
                            appendLine("匹配商品: ${comboSpuMatchResult.totalSpusMatched}")
                            appendLine(
                                "剩余商品: [${comboSpuMatchResult.remainingOrders.joinToString(" , ") { it.orderNo ?: "<id: ${it.id}>" }}]",
                            )
                            appendLine("套装化率: ${"%.2f".format(comboSpuMatchResult.totalSpusMatched.toDouble() / spuCodes.size * 100)}%")
                            appendLine("最优解  : ${comboSpuMatchResult.isOptimal}")
                            appendLine("执行时间: ${comboSpuMatchResult.executionTimeMs} ms")
                        }
                    log.info { "运单: ${waybill.id} | 匹配到套装: $matchMessage" }
                    comboSpuMatchResult.matchedComboResult.map { MatchedComboBillingUnitWrapper(waybill, it) } +
                        comboSpuMatchResult.remainingOrders.map { OrderBillingUnitWrapper(waybill, it) }
                } else {
                    subOrders.map { OrderBillingUnitWrapper(waybill, it) }
                }

            val country =
                waybill.recipient.country?.let {
                    countryRepository.findByName(it.lowercase(Locale.getDefault()))
                }
            calculateFlow.append(
                "  收件国家: ${country?.countryName ?: "未知:${waybill.recipient.country}"}\n",
            )

            var discountedPrice = BigDecimal.ZERO

            log.info { "开始计算运单未折扣费用 | TraceId: ${event.waybillId}" }

            val calculateCustomerNeedPayCostPreOrderDataList = mutableListOf<CalculateCustomerNeedPayCostPreOrderData>()

            calculateFlow.append("  计算未折扣费用:\n")
            val billingUnitsProvider = billingUnits.flatMap { it.getCustomerSkuArgumentProvider() }
            var unDiscountedPrice = BigDecimal.ZERO
            billingUnitsProvider.forEach { provider ->
                var remaining = provider.qty()
                val display = provider.display()

                var priceSum = BigDecimal.ZERO

                if (remaining == 0) {
                    calculateFlow.append(
                        "    $display: ? x $remaining = 0\n",
                    )
                } else {
                    while (remaining > 0) {
                        val (price, cost) = provider.getPrice(country?.id, remaining)
                        val pricePartSum = price.multiply(BigDecimal(cost))
                        calculateFlow.append(
                            "    $display: $price x $cost = $pricePartSum\n",
                        )
                        priceSum += pricePartSum
                        remaining -= cost
                    }
                    unDiscountedPrice += priceSum
                }

                val calculateCustomerNeedPayCostPreOrderData =
                    CalculateCustomerNeedPayCostPreOrderData(
                        orderNo = provider.order.orderNo!!,
                        uniqueCode = provider.order.orderNo!!,
                        productName = provider.order.product.cnName,
                        spu = provider.order.product.spu,
                        comboCustomerSpuId = (provider as? CustomerSkuArgumentProvider.ComboCustomerSkuProvider)?.comboCustomerSpu?.id,
                        comboCustomerSpu =
                            (provider as? CustomerSkuArgumentProvider.ComboCustomerSkuProvider)
                                ?.comboCustomerSpu
                                ?.systemSpuCodes
                                ?.joinToString(","),
                        comboCustomerSkuId = (provider as? CustomerSkuArgumentProvider.ComboCustomerSkuProvider)?.comboCustomerSku?.id,
                        comboCustomerSku =
                            (provider as? CustomerSkuArgumentProvider.ComboCustomerSkuProvider)
                                ?.comboCustomerSku
                                ?.systemSkuCode,
                        quantity = provider.qty(),
                        priceSum = priceSum,
                        vatTax = null,
                        additionalTax = null,
                        discounted = null,
                        comment = "",
                    )
                calculateCustomerNeedPayCostPreOrderDataList.add(calculateCustomerNeedPayCostPreOrderData)
            }
            calculateFlow.append("  计算未折扣费用完成, 费用: $unDiscountedPrice\n")

            log.info { "计算运单未折扣费用完成 | TraceId: ${event.waybillId} ｜ 费用: $unDiscountedPrice" }

            log.info { "开始匹配折扣规则: TraceId: ${event.waybillId}" }
            calculateFlow.append("  匹配折扣规则:\n")

            val minTriggerDiscountQuantity = billingUnitsProvider.minOf { it.minTriggerDiscountQuantity }
            calculateFlow.append("    最小触发数量: $minTriggerDiscountQuantity\n")
            val minDiscountPrice = billingUnitsProvider.minOf { it.discount }
            calculateFlow.append("    最小折扣价格: $minDiscountPrice\n")
            val allProductQty = subOrders.sumOf { it.product.qty }
            calculateFlow.append("    商品数量: $allProductQty\n")
            if (allProductQty >= minTriggerDiscountQuantity) {
                log.info {
                    "匹配到折扣规则 | 商品数量: $allProductQty | 最小触发数量: $minTriggerDiscountQuantity | TraceId: ${event.waybillId}"
                }
                discountedPrice =
                    minDiscountPrice.multiply(
                        BigDecimal(allProductQty - minTriggerDiscountQuantity + 1),
                    )
                calculateFlow.append(
                    "    折扣价格: $minDiscountPrice x ${allProductQty - minTriggerDiscountQuantity + 1} = $discountedPrice\n",
                )
                log.info { "计算折扣后费用完成 | TraceId: ${event.waybillId} ｜ 费用: $discountedPrice" }
            } else {
                calculateFlow.append("    未匹配到折扣规则\n")
                log.info {
                    "未匹配到折扣规则 | 商品数量: $allProductQty | 最小触发数量: $minTriggerDiscountQuantity  | TraceId: ${event.waybillId}"
                }
            }

            waybill.customerNeedPayCost = unDiscountedPrice - discountedPrice

            calculateCustomerNeedPayCostPreOrderDataList.replaceLast {
                copy(
                    discounted = discountedPrice,
                )
            }

            calculateFlow.append(
                "  计算折扣后费用完成, 费用: $unDiscountedPrice - $discountedPrice = ${waybill.customerNeedPayCost} \n",
            )
            log.info {
                "计算运单费用完成 | TraceId: ${event.waybillId} ｜ 费用: ${waybill.customerNeedPayCost}"
            }

            if (country != null) {
                log.info { "找到国家信息：${country.countryName} | TraceId: ${event.waybillId}" }
                log.info {
                    "开始计算目的地国家 ${waybill.recipient.country} 的税费 | TraceId: ${event.waybillId}"
                }
                calculateFlow.append("税费计算:\n")

                val taxConfig =
                    customerCountryTaxConfigRepository.findByCustomerIdAndCountryId(waybill.customerId, country.id) ?: run {
                        // 找不到特定国家的设置,尝试查找特殊的国家定义
                        val specialCountry =
                            countryRepository.findSpecial().find {
                                // 如果特殊国家定义的include里包含国际码,则使用该特殊国家定义(例如 特殊定义:欧洲)
                                it.includeIsoAlphaThrees.contains(country.isoAlphaThree)
                            }
                        if (specialCountry != null) {
                            customerCountryTaxConfigRepository.findByCustomerIdAndCountryId(waybill.customerId, specialCountry.id)
                        } else {
                            null
                        }
                    }
                if (taxConfig == null) {
                    log.warn { "未找到对应税务信息：${waybill.recipient.country} | TraceId: ${event.waybillId}" }
                    calculateFlow.append("  无法计算税费: 未找到对应税务信息\n")
                } else {
                    val totalVatTax = taxConfig.vatTax
                    var totalAdditionalTax = taxConfig.additionalTax

                    val findStateTaxConfig = taxConfig.findStateTaxConfig(waybill.recipient.state)
                    totalAdditionalTax += findStateTaxConfig
                    calculateFlow.append(
                        """
                                    |  汇总:
                                    |  VAT税: ¥${taxConfig.vatTax}            | ${waybill.recipient.country}
                                    |  附加税: ¥${taxConfig.additionalTax}     | ${waybill.recipient.country}
                                    |  州附加税: ¥$findStateTaxConfig          | ${waybill.recipient.state}
                                    |
                        """.trimMargin(),
                    )
                    calculateCustomerNeedPayCostPreOrderDataList.replaceLast {
                        copy(vatTax = totalVatTax, additionalTax = totalAdditionalTax)
                    }
                    waybill.customerNeedPayTax = totalVatTax + totalAdditionalTax
                    log.info {
                        "计算得到总税费：VAT税 $totalVatTax + 附加税 $totalAdditionalTax = ${waybill.customerNeedPayTax} | TraceId: ${event.waybillId}"
                    }
                }
            } else {
                log.warn { "未找到国家信息：${waybill.recipient.country} | TraceId: ${event.waybillId}" }
                calculateFlow.append("  无法计算税费: 未找到国家信息\n")
            }

            calculateFlow.append(
                """
                |费用汇总:
                |  商品成本: ¥${waybill.customerNeedPayCost}
                |  税费: ¥${waybill.customerNeedPayTax}
                |  应付总额: ¥${waybill.customerNeedPayCost + waybill.customerNeedPayTax}
                |
                """.trimMargin(),
            )

            waybill.calculateCustomerNeedPayCostData = CalculateCustomerNeedPayCostData(calculateCustomerNeedPayCostPreOrderDataList)

            waybill.calculateCustomerNeedPayCostFlow = calculateFlow.toString()
            log.info { "计算完成，保存运单信息 | TraceId: ${event.waybillId}" }
            waybillRepository.save(waybill)
        }
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
